// Casino data
const casinos = [
    {
        id: 1,
        name: "Royal Vegas Casino",
        logo: "RV",
        rating: 4.8,
        description: "Eines der beliebtesten Online Casinos mit über 700 Spielen und erstklassigem Kundenservice.",
        bonus: {
            title: "Willkommensbonus",
            details: "100% bis zu 500€ + 200 Freispiele"
        },
        features: [
            "Über 700 Spiele",
            "Live Casino",
            "Mobile App",
            "24/7 Support",
            "Schnelle Auszahlungen",
            "SSL Verschlüsselung"
        ],
        category: ["popular", "bonus"],
        isNew: false,
        link: "https://example.com/royal-vegas"
    },
    {
        id: 2,
        name: "Golden Palace",
        logo: "GP",
        rating: 4.6,
        description: "Luxuriöses Casino-Erlebnis mit exklusiven VIP-Programmen und hohen Gewinnchancen.",
        bonus: {
            title: "VIP Willkommenspaket",
            details: "200% bis zu 1000€ + 100 Freispiele"
        },
        features: [
            "VIP Programm",
            "Hohe Limits",
            "Exklusive Spiele",
            "Persönlicher Manager",
            "Wöchentliche Boni",
            "Cashback"
        ],
        category: ["bonus", "popular"],
        isNew: false,
        link: "https://example.com/golden-palace"
    },
    {
        id: 3,
        name: "Neon Slots",
        logo: "NS",
        rating: 4.5,
        description: "Modernes Casino mit den neuesten Slot-Spielen und innovativen Features.",
        bonus: {
            title: "Mega Freispiele",
            details: "50% bis zu 300€ + 300 Freispiele"
        },
        features: [
            "Neueste Slots",
            "Megaways Spiele",
            "Turniere",
            "Gamification",
            "Crypto Zahlungen",
            "Sofortspiel"
        ],
        category: ["new"],
        isNew: true,
        link: "https://example.com/neon-slots"
    },
    {
        id: 4,
        name: "Diamond Casino",
        logo: "DC",
        rating: 4.7,
        description: "Elegantes Casino mit klassischen Tischspielen und modernen Slots.",
        bonus: {
            title: "Diamant Bonus",
            details: "150% bis zu 750€ + 150 Freispiele"
        },
        features: [
            "Klassische Spiele",
            "Live Dealer",
            "Blackjack Varianten",
            "Roulette",
            "Poker Turniere",
            "Loyalty Programm"
        ],
        category: ["popular"],
        isNew: false,
        link: "https://example.com/diamond-casino"
    },
    {
        id: 5,
        name: "Cyber Casino",
        logo: "CC",
        rating: 4.4,
        description: "Futuristisches Casino mit Kryptowährungen und innovativen Spielkonzepten.",
        bonus: {
            title: "Crypto Bonus",
            details: "75% bis zu 400€ + 250 Freispiele"
        },
        features: [
            "Kryptowährungen",
            "Anonyme Spiele",
            "Blockchain Verifikation",
            "NFT Belohnungen",
            "Metaverse Integration",
            "Instant Withdrawals"
        ],
        category: ["new", "bonus"],
        isNew: true,
        link: "https://example.com/cyber-casino"
    }
];

// DOM elements
const casinosGrid = document.getElementById('casinos-grid');
const searchInput = document.getElementById('casino-search');
const filterButtons = document.querySelectorAll('.filter-btn');
const hamburger = document.querySelector('.hamburger');
const nav = document.querySelector('.nav');
const newsletterForm = document.querySelector('.newsletter-form');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    renderCasinos(casinos);
    initializeEventListeners();
    initializeAnimations();
});

// Render casino cards
function renderCasinos(casinosToRender) {
    casinosGrid.innerHTML = '';
    
    casinosToRender.forEach(casino => {
        const casinoCard = createCasinoCard(casino);
        casinosGrid.appendChild(casinoCard);
    });
    
    // Add stagger animation
    const cards = casinosGrid.querySelectorAll('.casino-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in');
    });
}

// Create individual casino card
function createCasinoCard(casino) {
    const card = document.createElement('div');
    card.className = 'casino-card';
    card.setAttribute('data-category', casino.category.join(' '));
    
    const stars = generateStars(casino.rating);
    const features = casino.features.map(feature => 
        `<li><i class="fas fa-check"></i>${feature}</li>`
    ).join('');
    
    card.innerHTML = `
        ${casino.isNew ? '<div class="casino-badge">NEU</div>' : ''}
        <div class="casino-header">
            <div class="casino-logo">${casino.logo}</div>
            <div class="casino-info">
                <h3>${casino.name}</h3>
                <div class="casino-rating">${stars}</div>
            </div>
        </div>
        <p class="casino-description">${casino.description}</p>
        <div class="bonus-info">
            <div class="bonus-title">${casino.bonus.title}</div>
            <div class="bonus-details">${casino.bonus.details}</div>
        </div>
        <div class="casino-features">
            <ul class="feature-list">${features}</ul>
        </div>
        <div class="casino-actions">
            <a href="${casino.link}" target="_blank" rel="noopener noreferrer" class="play-btn">
                Jetzt spielen
            </a>
        </div>
        <div class="legal-notice">
            18+ | Geschäftsbedingungen gelten
        </div>
    `;
    
    return card;
}

// Generate star rating
function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    let stars = '';
    
    for (let i = 0; i < fullStars; i++) {
        stars += '<span class="star">★</span>';
    }
    
    if (hasHalfStar) {
        stars += '<span class="star">☆</span>';
    }
    
    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
        stars += '<span class="star empty">☆</span>';
    }
    
    return stars;
}

// Initialize event listeners
function initializeEventListeners() {
    // Mobile menu toggle
    hamburger.addEventListener('click', () => {
        nav.classList.toggle('active');
        hamburger.classList.toggle('active');
    });
    
    // Search functionality
    searchInput.addEventListener('input', handleSearch);
    
    // Filter functionality
    filterButtons.forEach(button => {
        button.addEventListener('click', handleFilter);
    });
    
    // Newsletter form
    newsletterForm.addEventListener('submit', handleNewsletterSubmit);
    
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Close mobile menu when clicking on links
    document.querySelectorAll('.nav-list a').forEach(link => {
        link.addEventListener('click', () => {
            nav.classList.remove('active');
            hamburger.classList.remove('active');
        });
    });
}

// Handle search
function handleSearch(e) {
    const searchTerm = e.target.value.toLowerCase();
    const filteredCasinos = casinos.filter(casino => 
        casino.name.toLowerCase().includes(searchTerm) ||
        casino.description.toLowerCase().includes(searchTerm) ||
        casino.features.some(feature => feature.toLowerCase().includes(searchTerm))
    );
    
    renderCasinos(filteredCasinos);
}

// Handle filter
function handleFilter(e) {
    const filter = e.target.getAttribute('data-filter');
    
    // Update active button
    filterButtons.forEach(btn => btn.classList.remove('active'));
    e.target.classList.add('active');
    
    // Filter casinos
    let filteredCasinos;
    if (filter === 'all') {
        filteredCasinos = casinos;
    } else if (filter === 'new') {
        filteredCasinos = casinos.filter(casino => casino.isNew);
    } else {
        filteredCasinos = casinos.filter(casino => casino.category.includes(filter));
    }
    
    renderCasinos(filteredCasinos);
}

// Handle newsletter submission
function handleNewsletterSubmit(e) {
    e.preventDefault();
    const email = e.target.querySelector('input[type="email"]').value;
    
    // Simulate API call
    const button = e.target.querySelector('button');
    const originalText = button.textContent;
    button.innerHTML = '<span class="loading"></span>';
    button.disabled = true;
    
    setTimeout(() => {
        button.textContent = 'Erfolgreich!';
        button.style.background = 'var(--gradient-primary)';
        
        setTimeout(() => {
            button.textContent = originalText;
            button.style.background = '';
            button.disabled = false;
            e.target.reset();
        }, 2000);
    }, 1500);
}

// Initialize animations
function initializeAnimations() {
    // Add CSS for fade-in animation
    const style = document.createElement('style');
    style.textContent = `
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.6s ease forwards;
        }
        
        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    `;
    document.head.appendChild(style);
    
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // Observe sections for scroll animations
    document.querySelectorAll('.section-title, .newsletter').forEach(el => {
        observer.observe(el);
    });
}

// Add scroll effect to header
window.addEventListener('scroll', () => {
    const header = document.querySelector('.header');
    if (window.scrollY > 100) {
        header.style.background = 'rgba(26, 26, 26, 0.98)';
    } else {
        header.style.background = 'rgba(26, 26, 26, 0.95)';
    }
});

// Add click effect to buttons
document.addEventListener('click', (e) => {
    if (e.target.classList.contains('btn') || e.target.classList.contains('play-btn')) {
        const ripple = document.createElement('span');
        const rect = e.target.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        `;
        
        e.target.style.position = 'relative';
        e.target.style.overflow = 'hidden';
        e.target.appendChild(ripple);
        
        setTimeout(() => ripple.remove(), 600);
    }
});

// Add ripple animation CSS
const rippleStyle = document.createElement('style');
rippleStyle.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(rippleStyle);
