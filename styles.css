/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #d4af37;
    --secondary-color: #c41e3a;
    --accent-color: #ff6b35;
    --dark-bg: #0a0a0a;
    --dark-card: #1a1a1a;
    --text-light: #ffffff;
    --text-muted: #cccccc;
    --gradient-primary: linear-gradient(135deg, #d4af37, #ffd700);
    --gradient-secondary: linear-gradient(135deg, #c41e3a, #ff4757);
    --shadow-glow: 0 0 20px rgba(212, 175, 55, 0.3);
    --shadow-card: 0 8px 32px rgba(0, 0, 0, 0.3);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: 'Roboto', sans-serif;
    background: var(--dark-bg);
    color: var(--text-light);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

.logo-text {
    font-family: 'Orbitron', monospace;
    font-weight: 900;
    color: var(--primary-color);
    text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
}

.logo-main {
    display: block;
    font-size: 1.8rem;
    letter-spacing: 2px;
}

.logo-sub {
    display: block;
    font-size: 0.8rem;
    font-weight: 400;
    color: var(--text-muted);
    letter-spacing: 1px;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-list a {
    color: var(--text-light);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-list a:hover {
    color: var(--primary-color);
}

.nav-list a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: var(--transition);
}

.nav-list a:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--primary-color);
    transition: var(--transition);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    background: radial-gradient(ellipse at center, rgba(212, 175, 55, 0.1) 0%, var(--dark-bg) 70%);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-cards {
    position: absolute;
    width: 100%;
    height: 100%;
}

.card {
    position: absolute;
    font-size: 3rem;
    color: var(--primary-color);
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.card-1 { top: 20%; left: 10%; animation-delay: 0s; }
.card-2 { top: 60%; right: 15%; animation-delay: 1.5s; }
.card-3 { bottom: 30%; left: 20%; animation-delay: 3s; }
.card-4 { top: 40%; right: 30%; animation-delay: 4.5s; }

.dice {
    position: absolute;
    font-size: 2rem;
    color: var(--secondary-color);
    opacity: 0.15;
    animation: roll 8s linear infinite;
}

.dice-1 { top: 30%; left: 70%; }
.dice-2 { bottom: 40%; right: 60%; animation-delay: 4s; }

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes roll {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hero-content {
    text-align: center;
    z-index: 2;
    position: relative;
}

.hero-title {
    font-family: 'Orbitron', monospace;
    font-size: 3.5rem;
    font-weight: 900;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(212, 175, 55, 0.3);
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-muted);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    font-size: 1rem;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--dark-bg);
    box-shadow: var(--shadow-glow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 0 30px rgba(212, 175, 55, 0.5);
}

.btn-secondary {
    background: var(--gradient-secondary);
    color: var(--text-light);
}

.btn-glow {
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { box-shadow: 0 0 20px rgba(212, 175, 55, 0.3); }
    to { box-shadow: 0 0 30px rgba(212, 175, 55, 0.6); }
}

/* Search Section */
.search-section {
    padding: 2rem 0;
    background: rgba(26, 26, 26, 0.5);
    margin-top: 80px;
}

.search-bar {
    position: relative;
    max-width: 500px;
    margin: 0 auto 2rem;
}

.search-bar input {
    width: 100%;
    padding: 1rem 3rem 1rem 1rem;
    border: 2px solid rgba(212, 175, 55, 0.3);
    border-radius: var(--border-radius);
    background: var(--dark-card);
    color: var(--text-light);
    font-size: 1rem;
    transition: var(--transition);
}

.search-bar input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-glow);
}

.search-bar i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
}

.filter-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.5rem 1.5rem;
    border: 2px solid rgba(212, 175, 55, 0.3);
    border-radius: 25px;
    background: transparent;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
}

.filter-btn.active,
.filter-btn:hover {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: var(--dark-bg);
}

/* Section Titles */
.section-title {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: var(--primary-color);
    text-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
}

/* Casinos Section */
.casinos-section {
    padding: 4rem 0;
}

.casinos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Casino Card Styles */
.casino-card {
    background: var(--dark-card);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-card);
    border: 1px solid rgba(212, 175, 55, 0.2);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.casino-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
    transition: var(--transition);
}

.casino-card:hover::before {
    left: 100%;
}

.casino-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
    border-color: var(--primary-color);
}

.casino-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.casino-logo {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    color: var(--dark-bg);
    margin-right: 1rem;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.casino-info h3 {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.casino-rating {
    display: flex;
    gap: 0.2rem;
    margin-bottom: 0.5rem;
}

.star {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.star.empty {
    color: rgba(212, 175, 55, 0.3);
}

.casino-description {
    color: var(--text-muted);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.casino-features {
    margin-bottom: 1.5rem;
}

.feature-list {
    list-style: none;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

.feature-list li {
    display: flex;
    align-items: center;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.feature-list li i {
    color: var(--primary-color);
    margin-right: 0.5rem;
    width: 16px;
}

.bonus-info {
    background: rgba(212, 175, 55, 0.1);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid var(--primary-color);
}

.bonus-title {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.bonus-details {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.casino-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.play-btn {
    flex: 1;
    background: var(--gradient-primary);
    color: var(--dark-bg);
    padding: 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.play-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
}

.play-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: var(--transition);
}

.play-btn:active::after {
    width: 300px;
    height: 300px;
}

.casino-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--gradient-secondary);
    color: var(--text-light);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.legal-notice {
    font-size: 0.8rem;
    color: var(--text-muted);
    text-align: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(212, 175, 55, 0.2);
}

/* Newsletter Section */
.newsletter {
    background: var(--dark-card);
    padding: 4rem 0;
    text-align: center;
}

.newsletter-content h3 {
    font-family: 'Orbitron', monospace;
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.newsletter-content p {
    color: var(--text-muted);
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.newsletter-form {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
    gap: 1rem;
}

.newsletter-form input {
    flex: 1;
    padding: 1rem;
    border: 2px solid rgba(212, 175, 55, 0.3);
    border-radius: var(--border-radius);
    background: var(--dark-bg);
    color: var(--text-light);
    font-size: 1rem;
}

.newsletter-form input:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Footer Styles */
.footer {
    background: var(--dark-bg);
    border-top: 1px solid rgba(212, 175, 55, 0.2);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    font-family: 'Orbitron', monospace;
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.footer-section p {
    color: var(--text-muted);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: var(--text-muted);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(212, 175, 55, 0.1);
    border-radius: 50%;
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.social-links a:hover {
    background: var(--primary-color);
    color: var(--dark-bg);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid rgba(212, 175, 55, 0.2);
    padding-top: 2rem;
    text-align: center;
}

.responsible-gambling {
    margin-bottom: 1rem;
}

.responsible-gambling p {
    color: var(--text-muted);
    margin-bottom: 0.5rem;
}

.responsible-gambling strong {
    color: var(--secondary-color);
    font-weight: 600;
}

.gambling-logos {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    margin-top: 1rem;
}

.age-restriction {
    background: var(--secondary-color);
    color: var(--text-light);
    padding: 0.5rem 1rem;
    border-radius: 50%;
    font-weight: bold;
    font-size: 1.2rem;
}

.responsible-text {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
}

.copyright {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: var(--dark-card);
        border-top: 1px solid rgba(212, 175, 55, 0.2);
    }

    .nav.active {
        display: block;
    }

    .nav-list {
        flex-direction: column;
        padding: 1rem;
        gap: 1rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .casinos-grid {
        grid-template-columns: 1fr;
    }

    .casino-header {
        flex-direction: column;
        text-align: center;
    }

    .casino-logo {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .feature-list {
        grid-template-columns: 1fr;
    }

    .newsletter-form {
        flex-direction: column;
    }

    .filter-buttons {
        gap: 0.5rem;
    }

    .filter-btn {
        padding: 0.4rem 1rem;
        font-size: 0.9rem;
    }

    .gambling-logos {
        flex-direction: column;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .casino-card {
        padding: 1.5rem;
    }

    .casino-actions {
        flex-direction: column;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(212, 175, 55, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}
